#!/usr/bin/env python3
"""
步骤优化聚合器
负责处理测试用例步骤优化的业务逻辑
"""

from typing import Tuple, Optional
from openai import OpenAI
from loguru import logger

from config.globalconfig import get_or_create_settings_ins


class StepOptimizeAggregate:
    """步骤优化聚合器 - 处理步骤优化的核心业务逻辑"""

    def __init__(self):
        # 从配置文件读取设置
        config = get_or_create_settings_ins()

        # 豆包AI配置
        self.doubao_api_key = config.paths.doubao_api_key
        self.doubao_base_url = config.paths.doubao_base_url
        self.doubao_model = config.paths.doubao_model

        # 初始化OpenAI客户端
        self.ai_client = None
        if self.doubao_api_key:
            self.ai_client = OpenAI(
                base_url=self.doubao_base_url,
                api_key=self.doubao_api_key
            )

    def optimize_test_steps(self, steps: str) -> <PERSON><PERSON>[Optional[str], Optional[str]]:
        """
        使用AI优化测试用例步骤格式

        Args:
            steps: 需要优化的步骤内容

        Returns:
            (优化结果, 错误信息) 元组
        """
        try:
            if not self.ai_client:
                return None, "AI client not initialized, please check doubao_api_key configuration"

            logger.info(f"🔧 StepOptimizeAggregate: Starting step optimization for: {steps[:100]}...")

            # 构建优化提示
            system_instruction = self._build_optimization_prompt()

            messages = [
                {
                    "role": "system",
                    "content": system_instruction
                },
                {
                    "role": "user",
                    "content": steps
                }
            ]

            logger.info("🤖 StepOptimizeAggregate: Calling Doubao AI for step optimization...")

            response = self.ai_client.chat.completions.create(
                model=self.doubao_model,
                messages=messages,
                temperature=0
            )

            # 提取AI响应
            ai_result = response.choices[0].message.content
            logger.info(f"✅ StepOptimizeAggregate: Step optimization completed successfully")
            return ai_result, None

        except Exception as e:
            logger.error(f"❌ StepOptimizeAggregate: Failed to optimize steps with AI: {str(e)}")
            return None, str(e)

    def _build_optimization_prompt(self) -> str:
        """
        构建步骤优化的提示词

        Returns:
            优化提示词
        """
        return """你是一个测试用例步骤优化助手，请你按照如下要求优化步骤格式，并输出优化后的步骤内容。

优化要求
按照换行拆分步骤，一行一个步骤；如果步骤包含序号，去除序号。
如果步骤未换行，但每个步骤都有序号，则去除序号并在每个步骤之间添加换行。
禁止修改步骤文字内容，仅优化格式。

输出要求
仅输出优化后的步骤内容，每行一个步骤，不要在步骤末尾添加多余空格或空行。
输出为纯文本格式，除了步骤外不输出任何说明、标点或多余空白行。

执行示例： 

输入内容： 1.点击首页 \\n 2、点击下方'热门玩法' 3 点击右上角'我的房间'

优化后结果：

点击首页
点击下方'热门玩法'
点击右上角'我的房间'"""


# 创建全局实例
step_optimize_aggregate = StepOptimizeAggregate()
